# Weaviate Configuration
# Đặt URL của Weaviate API tùy theo môi trường
# Development: http://localhost:3000
# Production: https://api.example.com
BACKEND_API_URL=http://localhost:3000

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# PayOS - Không sử dụng NEXT_PUBLIC_ để bảo mật thông tin xác thực
# Thay đổi từ NEXT_PUBLIC_PAYOS_* sang PAYOS_* để bảo mật thông tin xác thực
PAYOS_CLIENT_ID=your-payos-client-id
PAYOS_API_KEY=your-payos-api-key
PAYOS_CHECKSUM_KEY=your-payos-checksum-key

# Facebook Integration
# App ID cần NEXT_PUBLIC_ để sử dụng ở client-side
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id
# App Secret chỉ dùng server-side (không có NEXT_PUBLIC_)
FACEBOOK_APP_SECRET=your_facebook_app_secret
# Webhook verify token cho Facebook webhook verification
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your_custom_verify_token

# OpenAI for Auto Reply
OPENAI_API_KEY=your_openai_api_key

# Mooly API Configuration
MOOLY_API_HOST=https://app.mooly.vn
MOOLY_API_VERSION=v1
MOOLY_PLATFORM_TOKEN=your-mooly-platform-token
MOOLY_AGENT_BOT_ID=5
N8N_ACCESS_TOKEN=your-n8n-access-token

# Supabase Management API
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
TOKEN_ACCESS_API=your-secure-api-access-token

# Các biến môi trường khác
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
NEXT_PUBLIC_ASSETS_DIR=
BUILD_STATIC_EXPORT=false
