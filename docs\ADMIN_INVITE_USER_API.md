# Admin Invite User API Documentation

## 📋 Tổng quan

API endpoint `/api/admin/invite-user` cho phép admin invite user mới vào hệ thống Supabase project. API này được bảo vệ bằng `TOKEN_ACCESS_API` và tự động tạo tenant mới cho mỗi user được invite.

## 🔐 Xác thực

API yêu cầu header xác thực:
```
Authorization: Bearer <TOKEN_ACCESS_API>
```

Token được cấu hình trong environment variable `TOKEN_ACCESS_API`.

## 🎯 Endpoint

### POST `/api/admin/invite-user`

Invite user mới vào hệ thống.

#### Request Headers
```
Content-Type: application/json
Authorization: Bearer <TOKEN_ACCESS_API>
```

#### Request Body
```json
{
  "email": "<EMAIL>",
  "name": "Tên User"
}
```

#### Response Success (201)
```json
{
  "success": true,
  "message": "User invited successfully. Tenant will be auto-created on first login.",
  "data": {
    "user_id": "uuid-string",
    "email": "<EMAIL>",
    "name": "Tên User",
    "invited_at": "2024-01-01T00:00:00.000Z",
    "confirmation_sent_at": "2024-01-01T00:00:00.000Z",
    "note": "User will become tenant owner with 200 welcome credits"
  }
}
```

#### Response Error Examples

**401 - Unauthorized**
```json
{
  "success": false,
  "error": "Missing or invalid Authorization header. Use: Bearer <TOKEN_ACCESS_API>",
  "code": "UNAUTHORIZED"
}
```

**403 - Invalid Token**
```json
{
  "success": false,
  "error": "Invalid access token",
  "code": "INVALID_TOKEN"
}
```

**400 - Validation Error**
```json
{
  "success": false,
  "error": "Missing required fields: email and name are required",
  "code": "VALIDATION_ERROR"
}
```

**409 - User Exists**
```json
{
  "success": false,
  "error": "User with this email already exists",
  "code": "USER_EXISTS"
}
```

### GET `/api/admin/invite-user`

Kiểm tra trạng thái API và xem hướng dẫn sử dụng.

#### Response (200)
```json
{
  "success": true,
  "message": "Invite User API is running",
  "endpoint": "/api/admin/invite-user",
  "method": "POST",
  "required_headers": {
    "Authorization": "Bearer <TOKEN_ACCESS_API>",
    "Content-Type": "application/json"
  },
  "required_body": {
    "email": "<EMAIL>",
    "name": "User Name"
  },
  "note": "User will automatically become tenant owner with new tenant and 200 welcome credits"
}
```

## 🔄 Quy trình tự động

Khi user được invite thành công, hệ thống sẽ tự động:

1. **Gửi email invite** - Supabase gửi email xác nhận đến user
2. **User click link xác nhận** - User truy cập link trong email
3. **Trigger `handle_new_user()` chạy** - Tự động thực hiện:
   - Tạo tenant mới với tên từ user name
   - Tạo user record trong `public.users` với `is_tenant_owner = true`
   - Cấp 200 credits welcome vào `tenant_credits`
   - Tạo credit transaction ghi nhận welcome bonus
   - Update auth metadata với `tenant_id`

## 🛠️ Cấu hình Environment

Thêm vào file `.env`:
```env
TOKEN_ACCESS_API=your-secure-api-access-token
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
```

## 📝 Ví dụ sử dụng

### cURL
```bash
curl -X POST http://localhost:3000/api/admin/invite-user \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token-access-api" \
  -d '{
    "email": "<EMAIL>",
    "name": "New User"
  }'
```

### JavaScript/Fetch
```javascript
const response = await fetch('/api/admin/invite-user', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token-access-api'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    name: 'New User'
  })
});

const result = await response.json();
console.log(result);
```

## 🔒 Bảo mật

- API chỉ chấp nhận requests có `TOKEN_ACCESS_API` hợp lệ
- Email được mask trong logs để bảo mật
- Có logging chi tiết cho audit trail
- Rate limiting có thể được thêm vào sau

## 📊 Logging

API ghi log các sự kiện quan trọng:
- Requests với token không hợp lệ
- User invite thành công
- Lỗi Supabase invite
- Thông tin debug (email được mask)

## ⚠️ Lưu ý quan trọng

1. **Không cần role parameter** - Mọi user được invite đều trở thành tenant owner
2. **Tenant tự động tạo** - Mỗi user sẽ có tenant riêng
3. **200 credits welcome** - Tự động cấp khi user đăng nhập lần đầu
4. **Email unique** - Không thể invite user với email đã tồn tại
5. **Redirect URL** - User sẽ được redirect về `/auth/callback` sau khi xác nhận

## 🚀 Production Ready

API đã được tối ưu cho production với:
- Error handling đầy đủ
- Security logging
- Input validation
- Proper HTTP status codes
- Structured response format
