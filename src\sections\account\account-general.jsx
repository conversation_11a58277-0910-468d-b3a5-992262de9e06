'use client';

import { z as zod } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { isValidPhoneNumber } from 'react-phone-number-input/input';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import { fData } from 'src/utils/format-number';
import { createClient } from 'src/utils/supabase/client';

import { useProfileMutations } from 'src/actions/mooly-chatbot/profile-service';

import { toast } from 'src/components/snackbar';
import { Form, Field, schemaHelper } from 'src/components/hook-form';

import { useAuthContext } from 'src/auth/hooks';

// ----------------------------------------------------------------------

export const UpdateUserSchema = zod.object({
  displayName: zod.string().min(1, { message: 'Tên là bắt buộc!' }),
  email: zod
    .string()
    .min(1, { message: 'Email là bắt buộc!' })
    .email({ message: 'Email không hợp lệ!' }),
  photoURL: schemaHelper.file().optional(),
  phoneNumber: schemaHelper.phoneNumber({ isValid: isValidPhoneNumber }),
  // Optional fields
  country: zod.string().optional(),
  address: zod.string().optional(),
  state: zod.string().optional(),
  city: zod.string().optional(),
  zipCode: zod.string().optional(),
  about: zod.string().optional(),
  isPublic: zod.boolean().optional(),
});

// ----------------------------------------------------------------------

export function AccountGeneral() {
  const { user, refreshUserData } = useAuthContext();
  const { updateProfile, isUpdating } = useProfileMutations();

  const currentUser = {
    displayName: user?.displayName || '',
    email: user?.email || '',
    photoURL: user?.photoURL,
    phoneNumber: user?.phoneNumber || '',
    country: user?.country || 'VN', // Mặc định Việt Nam
    address: user?.address || '',
    state: user?.state || '',
    city: user?.city || '',
    zipCode: user?.zipCode || '',
    about: user?.about || '',
    isPublic: user?.isPublic || false,
  };

  const defaultValues = {
    displayName: '',
    email: '',
    photoURL: null,
    phoneNumber: '',
    country: 'VN', // Mặc định Việt Nam
    address: '',
    state: '',
    city: '',
    zipCode: '',
    about: '',
    isPublic: false,
  };

  const methods = useForm({
    mode: 'all',
    resolver: zodResolver(UpdateUserSchema),
    defaultValues,
    values: currentUser,
  });

  const {
    handleSubmit,
    setValue,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      // Cập nhật thông tin người dùng
      const result = await updateProfile(user?.id, data);

      if (result.success) {
        toast.success('Cập nhật thông tin thành công!');

        // Refresh user data from database without page reload
        try {
          const supabase = createClient();
          await supabase.auth.refreshSession();

          // Use the refreshUserData function from auth context
          if (refreshUserData) {
            await refreshUserData();
          } else {
            // Fallback: reload page if refreshUserData is not available
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }
        } catch (refreshError) {
          console.warn('Could not refresh user data:', refreshError);
          // Fallback to page reload if refresh fails
          setTimeout(() => {
            window.location.reload();
          }, 500);
        }
      } else {
        toast.error(result.error?.message || 'Có lỗi xảy ra khi cập nhật thông tin');
      }
    } catch (error) {
      console.error(error);
      toast.error('Có lỗi xảy ra khi cập nhật thông tin');
    }
  });

  return (
    <Form methods={methods} onSubmit={onSubmit}>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card
            sx={{
              pt: 6,
              pb: 4,
              px: 3,
              textAlign: 'center',
            }}
          >
            <Typography variant="h6" sx={{ mb: 3, color: 'text.primary' }}>
              Ảnh đại diện
            </Typography>

            <Field.UploadAvatar
              name="photoURL"
              maxSize={3145728}
              loading={isUpdating}
              onDrop={(acceptedFiles) => {
                const file = acceptedFiles[0];
                if (file) {
                  // Chỉ lưu file vào form state, sẽ upload khi user bấm lưu
                  setValue('photoURL', file);
                  toast.info('Ảnh đã được chọn. Bấm "Lưu thay đổi" để cập nhật.');
                }
              }}
              helperText={
                <Typography
                  variant="caption"
                  sx={{
                    mt: 2,
                    mx: 'auto',
                    display: 'block',
                    textAlign: 'center',
                    color: 'text.disabled',
                  }}
                >
                  Hỗ trợ: *.jpeg, *.jpg, *.png, *.gif
                  <br /> Tối đa {fData(3145728)}
                  <br /> Chọn ảnh và bấm &quot;Lưu thay đổi&quot;
                </Typography>
              }
            />

            <Field.Switch
              name="isPublic"
              labelPlacement="start"
              label="Hồ sơ công khai"
              sx={{ mt: 4 }}
            />
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 8 }}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, color: 'text.primary' }}>
              Thông tin cá nhân
            </Typography>

            {/* Các field bắt buộc */}
            <Box
              sx={{
                rowGap: 3,
                columnGap: 2,
                display: 'grid',
                gridTemplateColumns: { xs: 'repeat(1, 1fr)', sm: 'repeat(2, 1fr)' },
                mb: 4,
              }}
            >
              <Field.Text
                name="displayName"
                label="Tên hiển thị"
                placeholder="Nhập tên của bạn"
                required
              />
              <Field.Text
                name="email"
                label="Email"
                placeholder="<EMAIL>"
                required
              />
              <Field.Phone
                name="phoneNumber"
                label="Số điện thoại"
                placeholder="Nhập số điện thoại"
                country="VN"
                required
              />
              <Field.CountrySelect
                name="country"
                label="Quốc gia"
                placeholder="Chọn quốc gia"
              />
            </Box>

            {/* Các field tùy chọn - có thể ẩn/hiện */}
            <Typography variant="subtitle2" sx={{ mb: 2, color: 'text.secondary' }}>
              Thông tin bổ sung (tùy chọn)
            </Typography>

            <Box
              sx={{
                rowGap: 3,
                columnGap: 2,
                display: 'grid',
                gridTemplateColumns: { xs: 'repeat(1, 1fr)', sm: 'repeat(2, 1fr)' },
                mb: 3,
              }}
            >
              <Field.Text
                name="address"
                label="Địa chỉ"
                placeholder="Nhập địa chỉ"
              />
              <Field.Text
                name="city"
                label="Thành phố"
                placeholder="Nhập thành phố"
              />
              <Field.Text
                name="state"
                label="Tỉnh/Thành"
                placeholder="Nhập tỉnh/thành"
              />
              <Field.Text
                name="zipCode"
                label="Mã bưu điện"
                placeholder="Nhập mã bưu điện"
              />
            </Box>

            <Field.Text
              name="about"
              multiline
              rows={3}
              label="Giới thiệu"
              placeholder="Viết vài dòng giới thiệu về bản thân..."
              sx={{ mb: 3 }}
            />

            <Stack direction="row" spacing={2} sx={{ justifyContent: 'flex-end' }}>
              <Button
                type="submit"
                variant="contained"
                loading={isSubmitting || isUpdating}
                size="large"
                sx={{ minWidth: 120 }}
              >
                Lưu thay đổi
              </Button>
            </Stack>
          </Card>
        </Grid>
      </Grid>
    </Form>
  );
}
