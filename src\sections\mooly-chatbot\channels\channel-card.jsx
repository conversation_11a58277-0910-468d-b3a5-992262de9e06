'use client';

import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import Switch from '@mui/material/Switch';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import { alpha, useTheme } from '@mui/material/styles';

import { Label } from 'src/components/label';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export default function ChannelCard({ channel, onConnect, onToggleActive, onAutomation }) {
  const theme = useTheme();
  const [pulseEffect, setPulseEffect] = useState(false);

  const { name, channelType, avatarUrl, websiteUrl, connected, connection, chatbot } = channel;

  // Hiệu ứng nhấp nháy nhẹ khi bot đang hoạt động
  useEffect(() => {
    if (connected && connection?.isActive) {
      const interval = setInterval(() => {
        setPulseEffect((prev) => !prev);
      }, 3000);

      return () => clearInterval(interval);
    }
    return () => {};
  }, [connected, connection?.isActive]);

  // Xử lý toggle trạng thái kích hoạt
  const handleToggleActive = async () => {
    try {
      const result = await onToggleActive(connection);
      if (result.success) {
        toast.success(`${connection.isActive ? 'Vô hiệu hóa' : 'Kích hoạt'} channel thành công!`);
      } else {
        toast.error(`Lỗi: ${result.error}`, { variant: 'error' });
      }
    } catch (error) {
      console.error(error);
      toast.error(`Lỗi: ${error.message}`, { variant: 'error' });
    }
  };

  return (
    <Card
      sx={{
        p: 0,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        boxShadow:
          connected && connection?.isActive
            ? `0 0 ${pulseEffect ? '12px' : '8px'} ${alpha(theme.palette.success.main, pulseEffect ? 0.6 : 0.4)}`
            : undefined,
        borderLeft: connected
          ? `4px solid ${connection?.isActive ? theme.palette.success.main : theme.palette.warning.main}`
          : undefined,
        transition: 'box-shadow 0.5s ease-in-out',
      }}
    >
      <CardContent sx={{ p: 3, pb: 1 }}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar
            alt={name}
            src={avatarUrl}
            sx={{
              width: 64,
              height: 64,
              bgcolor: () => alpha(theme.palette.primary.main, 0.1),
              color: 'primary.main',
            }}
          >
            {!avatarUrl && <Iconify icon="solar:chat-round-dots-bold" width={32} />}
          </Avatar>

          <Stack spacing={0.5} flexGrow={1}>
            <Typography variant="subtitle1" noWrap>
              {name}
            </Typography>

            <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
              {channelType || 'Website'}
            </Typography>

            {websiteUrl && (
              <Typography variant="caption" sx={{ color: 'text.disabled' }} noWrap>
                {websiteUrl}
              </Typography>
            )}
          </Stack>
        </Stack>

        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{ mt: 3, mb: 1 }}
        >
          <Typography variant="subtitle2">Trạng thái</Typography>
          {connected ? (
            <Label
              variant="filled"
              color={connection.isActive ? 'success' : 'warning'}
              sx={{
                textTransform: 'capitalize',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              <Iconify
                icon={connection.isActive ? 'eva:checkmark-circle-2-fill' : 'eva:slash-outline'}
                width={16}
              />
              {connection.isActive ? 'Đang hoạt động' : 'Đã tắt'}
            </Label>
          ) : (
            <Label
              variant="soft"
              color="error"
              sx={{
                textTransform: 'capitalize',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              <Iconify icon="eva:close-circle-fill" width={16} />
              Chưa kết nối
            </Label>
          )}
        </Stack>

        {connected && (
          <>
            <Divider sx={{ my: 1, borderStyle: 'dashed' }} />

            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Typography variant="subtitle2">Chatbot</Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {chatbot?.name || 'Không xác định'}
              </Typography>
            </Stack>
          </>
        )}
      </CardContent>

      {/* Thêm các nút action ở dưới card */}
      <Box sx={{ p: 2, mt: 'auto' }}>
        {connected ? (
          <Stack spacing={1}>
            {/* Hàng đầu tiên: Chỉnh sửa và Switch */}
            <Stack direction="row" spacing={1} justifyContent="space-between" alignItems="center">
              <Button
                variant="outlined"
                size="small"
                startIcon={<Iconify icon="eva:edit-fill" />}
                onClick={() => onConnect(channel)}
              >
                Chỉnh sửa
              </Button>

              <Stack direction="row" spacing={1} alignItems="center">
                <Typography
                  variant="caption"
                  sx={{
                    color: connection.isActive ? 'success.main' : 'text.secondary',
                    fontWeight: connection.isActive ? 'bold' : 'normal',
                  }}
                >
                  {connection.isActive ? 'Bật' : 'Tắt'}
                </Typography>
                <Switch
                  size="small"
                  checked={connection.isActive}
                  onChange={handleToggleActive}
                  color="success"
                  sx={{
                    '& .MuiSwitch-switchBase.Mui-checked': {
                      color: theme.palette.success.main,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.success.main, 0.1),
                      },
                    },
                    '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                      backgroundColor: theme.palette.success.main,
                    },
                    '& .MuiSwitch-thumb': {
                      boxShadow: connection.isActive
                        ? `0 0 4px ${theme.palette.success.main}`
                        : 'none',
                    },
                  }}
                />
              </Stack>
            </Stack>

            {/* Hàng thứ hai: Nút Automation */}
            <Button
              fullWidth
              variant="outlined"
              size="small"
              color="info"
              startIcon={<Iconify icon="solar:settings-bold-duotone" />}
              onClick={() => onAutomation && onAutomation(channel)}
              disabled={!connection?.id}
              sx={{
                borderStyle: 'dashed',
                '&:hover': {
                  borderStyle: 'solid',
                }
              }}
            >
              Cấu hình Automation
            </Button>
          </Stack>
        ) : (
          <Button
            fullWidth
            variant="contained"
            color="primary"
            startIcon={<Iconify icon="eva:link-2-fill" />}
            onClick={() => onConnect(channel)}
          >
            Kết nối
          </Button>
        )}
      </Box>
    </Card>
  );
}

ChannelCard.propTypes = {
  channel: PropTypes.object,
  onConnect: PropTypes.func,
  onToggleActive: PropTypes.func,
  onAutomation: PropTypes.func,
};
