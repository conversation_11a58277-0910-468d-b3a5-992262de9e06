'use client';

import PropTypes from 'prop-types';
import { useMemo, useState, useEffect, useContext, useCallback, createContext } from 'react';

import { getChatbots } from 'src/actions/mooly-chatbot/chatbot-service';
import { fetchMoolyChannels } from 'src/actions/mooly-chatbot/mooly-api-service';
import {
  getMoolyAccount,
  createMoolyAccount,
  updateMoolyAccount,
} from 'src/actions/mooly-chatbot/mooly-account-service';
import {
  getChatbotChannels,
  createChatbotChannel,
  updateChatbotChannel,
  deleteChatbotChannel,
} from 'src/actions/mooly-chatbot/chatbot-channel-service';

// ----------------------------------------------------------------------

// Tạo context cho channel
const ChannelContext = createContext(null);

/**
 * Provider cho channel context
 * @param {Object} props - Props của component
 * @returns {JSX.Element} - Provider component
 */
export function ChannelProvider({ children }) {
  // State cho dialog
  const [openAccountDialog, setOpenAccountDialog] = useState(false);
  const [openRegisterDialog, setOpenRegisterDialog] = useState(false);
  const [openConnectDialog, setOpenConnectDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openAutomationDialog, setOpenAutomationDialog] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState(null);

  // State cho dữ liệu
  const [account, setAccount] = useState(null);
  const [chatbots, setChatbots] = useState([]);
  const [moolyChannels, setMoolyChannels] = useState([]);
  const [connectedChannels, setConnectedChannels] = useState([]);

  // State cho loading
  const [isLoading, setIsLoading] = useState(true);
  const [isCreatingAccount, setIsCreatingAccount] = useState(false);
  const [isUpdatingAccount] = useState(false); // Keep for API compatibility
  const [isCreatingChannel, setIsCreatingChannel] = useState(false);
  const [isUpdatingChannel, setIsUpdatingChannel] = useState(false);
  const [isDeletingChannel, setIsDeletingChannel] = useState(false);

  // Tải dữ liệu ban đầu khi component mount
  useEffect(() => {
    // Hàm tải tất cả dữ liệu cần thiết
    const loadInitialData = async () => {
      setIsLoading(true);

      try {
        // 1. Tải thông tin tài khoản Mooly
        const accountResult = await getMoolyAccount();
        if (accountResult.success) {
          setAccount(accountResult.data);

          // 2. Nếu có tài khoản, tải danh sách kênh từ API Mooly
          if (accountResult.data?.accountId && accountResult.data?.token) {
            const moolyChannelsResult = await fetchMoolyChannels(
              accountResult.data.accountId,
              accountResult.data.token
            );

            if (moolyChannelsResult.success) {
              // Xử lý cấu trúc dữ liệu từ API Mooly
              const channelsData = moolyChannelsResult.data || [];
              setMoolyChannels(channelsData);
            }
          }
        }

        // 3. Tải danh sách chatbot
        const chatbotsResult = await getChatbots();
        if (chatbotsResult.success) {
          setChatbots(chatbotsResult.data || []);
        }

        // 4. Tải danh sách kênh đã kết nối
        const connectedChannelsResult = await getChatbotChannels();
        if (connectedChannelsResult.success) {
          setConnectedChannels(connectedChannelsResult.data || []);
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Xử lý mở dialog tài khoản
  const handleOpenAccountDialog = useCallback(() => {
    setOpenAccountDialog(true);
  }, []);

  // Xử lý đóng dialog tài khoản
  const handleCloseAccountDialog = useCallback(() => {
    setOpenAccountDialog(false);
  }, []);

  // Xử lý mở dialog đăng ký tài khoản
  const handleOpenRegisterDialog = useCallback(() => {
    setOpenRegisterDialog(true);
  }, []);

  // Xử lý đóng dialog đăng ký tài khoản
  const handleCloseRegisterDialog = useCallback(() => {
    setOpenRegisterDialog(false);
  }, []);

  // Xử lý mở dialog kết nối
  const handleOpenConnectDialog = useCallback((channel = null) => {
    setSelectedChannel(channel);
    setOpenConnectDialog(true);
  }, []);

  // Xử lý đóng dialog kết nối
  const handleCloseConnectDialog = useCallback(() => {
    setSelectedChannel(null);
    setOpenConnectDialog(false);
  }, []);

  // Xử lý mở dialog xóa
  const handleOpenDeleteDialog = useCallback((channel) => {
    setSelectedChannel(channel);
    setOpenDeleteDialog(true);
  }, []);

  // Xử lý đóng dialog xóa
  const handleCloseDeleteDialog = useCallback(() => {
    setSelectedChannel(null);
    setOpenDeleteDialog(false);
  }, []);

  // Xử lý mở dialog automation
  const handleOpenAutomationDialog = useCallback((channel) => {
    setSelectedChannel(channel);
    setOpenAutomationDialog(true);
  }, []);

  // Xử lý đóng dialog automation
  const handleCloseAutomationDialog = useCallback(() => {
    setSelectedChannel(null);
    setOpenAutomationDialog(false);
  }, []);

  // Xử lý kết nối channel
  const handleConnectChannel = useCallback(
    async (data) => {
      try {
        setIsCreatingChannel(true);

        // Tìm channel đã kết nối với inboxId này
        const existingChannel = connectedChannels.find(
          (channel) => channel.inboxId === data.inboxId
        );

        let result;
        if (existingChannel) {
          // Cập nhật kết nối hiện có
          result = await updateChatbotChannel(existingChannel.id, data);
        } else {
          // Tạo kết nối mới
          result = await createChatbotChannel(data);
        }
        if (result.success) {
          // Cập nhật state local thay vì gọi API
          if (existingChannel) {
            // Cập nhật channel trong danh sách
            const updatedConnectedChannels = connectedChannels.map((channel) =>
              channel.id === existingChannel.id ? result.data[0] : channel
            );
            setConnectedChannels(updatedConnectedChannels);
          } else {
            // Thêm channel mới vào danh sách
            const updatedConnectedChannels = [...connectedChannels, result.data[0]];
            setConnectedChannels(updatedConnectedChannels);
          }

          // Đóng dialog
          handleCloseConnectDialog();
          return { success: true };
        }

        return { success: false, error: result.error };
      } catch (error) {
        return { success: false, error: error.message };
      } finally {
        setIsCreatingChannel(false);
      }
    },
    [connectedChannels, handleCloseConnectDialog]
  );

  // Xử lý xóa kết nối channel
  const handleDeleteChannel = useCallback(async () => {
    if (!selectedChannel) return { success: false, error: 'No channel selected' };

    try {
      setIsDeletingChannel(true);
      const result = await deleteChatbotChannel(selectedChannel.id);

      if (result.success) {
        // Cập nhật state local thay vì gọi API
        setConnectedChannels(
          connectedChannels.filter((channel) => channel.id !== selectedChannel.id)
        );

        handleCloseDeleteDialog();
        return { success: true };
      }

      return { success: false, error: result.error };
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setIsDeletingChannel(false);
    }
  }, [selectedChannel, connectedChannels, handleCloseDeleteDialog]);

  // Xử lý toggle trạng thái kích hoạt của channel
  const handleToggleActive = useCallback(
    async (channel) => {
      try {
        setIsUpdatingChannel(true);

        // 1. Cập nhật trạng thái trong database
        const result = await updateChatbotChannel(channel.id, {
          ...channel,
          isActive: !channel.isActive,
        });

        if (result.success) {
          // 2. Gọi API Mooly để bật/tắt agent bot
          if (account?.accountId && account?.token) {
            const newIsActive = !channel.isActive;

            // Gọi API để bật/tắt agent bot (agent bot ID được cấu hình ở server-side)
            const setAgentBotResponse = await fetch('/api/mooly-proxy/channels/set-agent-bot', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                accountId: account.accountId,
                inboxId: channel.inboxId,
                token: account.token,
                agentBot: newIsActive, // null để tắt, true để bật (ID được cấu hình ở server)
              }),
            });

            const setAgentBotResult = await setAgentBotResponse.json();

            if (!setAgentBotResult.success) {
              console.error('Failed to set agent bot:', setAgentBotResult.error);
              // Vẫn tiếp tục vì đã cập nhật database thành công
            }
          }

          // 3. Cập nhật state local thay vì gọi API
          setConnectedChannels(
            connectedChannels.map((item) => (item.id === channel.id ? result.data[0] : item))
          );

          return { success: true };
        }

        return { success: false, error: result.error };
      } catch (error) {
        console.error('Error toggling channel active state:', error);
        return { success: false, error: error.message };
      } finally {
        setIsUpdatingChannel(false);
      }
    },
    [connectedChannels, account]
  );

  // Tạo danh sách kênh đã kết hợp thông tin
  const mergedChannels = useMemo(() => {
    if (isLoading || !moolyChannels || !connectedChannels || !chatbots) return [];
    try {
      // Tạo map các kênh đã kết nối theo inboxId
      const connectedMap = new Map();
      connectedChannels.forEach((channel) => {
        if (channel && channel.inboxId) {
          connectedMap.set(channel.inboxId, channel);
        }
      });

      // Tạo map các chatbot theo id
      const chatbotMap = new Map();
      chatbots.forEach((chatbot) => {
        if (chatbot && chatbot.id) {
          chatbotMap.set(chatbot.id, chatbot);
        }
      });

      // Xử lý cấu trúc dữ liệu moolyChannels
      // Có thể là array trực tiếp hoặc object có property payload
      let channelsArray = [];

      if (Array.isArray(moolyChannels)) {
        channelsArray = moolyChannels;
      } else if (moolyChannels?.payload && Array.isArray(moolyChannels.payload)) {
        channelsArray = moolyChannels.payload;
      } else if (moolyChannels?.data && Array.isArray(moolyChannels.data)) {
        channelsArray = moolyChannels.data;
      } else {
        console.warn('moolyChannels structure not recognized:', moolyChannels);
        return [];
      }

      // Đảm bảo channelsArray là array
      if (!Array.isArray(channelsArray)) {
        console.warn('channelsArray is not an array:', channelsArray);
        return [];
      }

      // Kết hợp thông tin
      return channelsArray
        .map((channel) => {
          if (!channel || !channel.id) return null;

          const connectedChannel = connectedMap.get(channel.id.toString());
          const chatbot =
            connectedChannel && connectedChannel.botId
              ? chatbotMap.get(connectedChannel.botId)
              : null;

          return {
            ...channel,
            connected: !!connectedChannel,
            connection: connectedChannel,
            chatbot,
          };
        })
        .filter(Boolean); // Lọc bỏ các giá trị null
    } catch (error) {
      console.error('Error merging channels:', error);
      return [];
    }
  }, [moolyChannels, connectedChannels, chatbots, isLoading]);

  // Hàm refresh dữ liệu thủ công (chỉ sử dụng khi cần thiết)
  const refreshAllData = useCallback(async () => {
    setIsLoading(true);

    try {
      // Tải lại tất cả dữ liệu
      const [accountResult, chatbotsResult, connectedChannelsResult] = await Promise.all([
        getMoolyAccount(),
        getChatbots(),
        getChatbotChannels(),
      ]);

      if (accountResult.success) {
        setAccount(accountResult.data);

        // Nếu có tài khoản, tải danh sách kênh từ API Mooly
        if (accountResult.data?.accountId && accountResult.data?.token) {
          const moolyChannelsResult = await fetchMoolyChannels(
            accountResult.data.accountId,
            accountResult.data.token
          );

          if (moolyChannelsResult.success) {
            // Xử lý cấu trúc dữ liệu từ API Mooly
            const channelsData = moolyChannelsResult.data || [];
            setMoolyChannels(channelsData);
          }
        }
      }

      if (chatbotsResult.success) {
        setChatbots(chatbotsResult.data || []);
      }

      if (connectedChannelsResult.success) {
        setConnectedChannels(connectedChannelsResult.data || []);
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Giá trị context
  const contextValue = {
    // Dữ liệu
    account,
    chatbots,
    moolyChannels,
    connectedChannels,
    mergedChannels,
    selectedChannel,

    // Trạng thái loading
    isLoading,
    isCreatingAccount,
    isUpdatingAccount,
    isCreatingChannel,
    isUpdatingChannel,
    isDeletingChannel,

    // Trạng thái dialog
    openAccountDialog,
    openRegisterDialog,
    openConnectDialog,
    openDeleteDialog,
    openAutomationDialog,

    // Hàm xử lý dialog
    handleOpenAccountDialog,
    handleCloseAccountDialog,
    handleOpenRegisterDialog,
    handleCloseRegisterDialog,
    handleOpenConnectDialog,
    handleCloseConnectDialog,
    handleOpenDeleteDialog,
    handleCloseDeleteDialog,
    handleOpenAutomationDialog,
    handleCloseAutomationDialog,

    // Hàm xử lý dữ liệu
    handleConnectChannel,
    handleDeleteChannel,
    handleToggleActive,

    // Hàm refresh dữ liệu (chỉ sử dụng khi cần thiết)
    refreshAllData,
  };

  return <ChannelContext.Provider value={contextValue}>{children}</ChannelContext.Provider>;
}

ChannelProvider.propTypes = {
  children: PropTypes.node,
};

/**
 * Hook để sử dụng channel context
 * @returns {Object} - Channel context
 */
export function useChannelContext() {
  const context = useContext(ChannelContext);
  if (!context) {
    throw new Error('useChannelContext must be used within a ChannelProvider');
  }
  return context;
}
