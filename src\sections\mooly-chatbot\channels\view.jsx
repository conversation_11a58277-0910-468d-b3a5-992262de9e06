'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Card from '@mui/material/Card';
import Tabs from '@mui/material/Tabs';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import AlertTitle from '@mui/material/AlertTitle';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';
import { LoadingScreen } from 'src/components/loading-screen';

import ChannelCard from './channel-card';
import MoolyRegisterDialog from './mooly-register-dialog';
import ChannelDeleteDialog from './channel-delete-dialog';
import ChannelConnectDialog from './channel-connect-dialog';
import ChannelAutomationDialog from './channel-automation-dialog';
import { ChannelProvider, useChannelContext } from './channel-context';

// ----------------------------------------------------------------------

export function ChannelsView() {
  return (
    <ChannelProvider>
      <ChannelsContent />
    </ChannelProvider>
  );
}

// ----------------------------------------------------------------------

function ChannelsContent() {
  const settings = useSettingsContext();
  const [tabValue, setTabValue] = useState(0); // 0: Tất cả, 1: Đã kết nối, 2: Chưa kết nối

  const {
    // Dữ liệu
    account,
    chatbots,
    mergedChannels,
    selectedChannel,

    // Trạng thái loading
    isLoading,
    isLoadingAccount,
    isLoadingChatbots,
    isLoadingMoolyChannels,
    isLoadingConnectedChannels,
    isCreatingChannel,
    isUpdatingChannel,
    isDeletingChannel,

    // Trạng thái dialog
    openRegisterDialog,
    openConnectDialog,
    openDeleteDialog,
    openAutomationDialog,

    // Hàm xử lý dialog
    handleOpenRegisterDialog,
    handleCloseRegisterDialog,
    handleOpenConnectDialog,
    handleCloseConnectDialog,
    handleCloseDeleteDialog,
    handleOpenAutomationDialog,
    handleCloseAutomationDialog,

    // Hàm xử lý dữ liệu
    handleConnectChannel,
    handleDeleteChannel,
    handleToggleActive,
  } = useChannelContext();

  // Lọc channels theo tab
  const filteredChannels = mergedChannels.filter((channel) => {
    if (tabValue === 0) return true; // Tất cả
    if (tabValue === 1) return channel.connected; // Đã kết nối
    if (tabValue === 2) return !channel.connected; // Chưa kết nối
    return true;
  });

  // Kiểm tra nếu không có dữ liệu
  const isNotFound = !filteredChannels.length;

  // Xử lý thay đổi tab
  const handleChangeTab = (_, newValue) => {
    setTabValue(newValue);
  };

  // Kiểm tra cấu hình labels khi account thay đổi
  useEffect(() => {
    if (account?.accountId && account?.token) {
      checkLabelsConfig(account);
    }
  }, [account]);

  // Hàm kiểm tra cấu hình labels
  const checkLabelsConfig = async (accountData) => {
    try {
      const response = await fetch(`/api/mooly-proxy/get-labels?account_id=${accountData.accountId}`);
      const result = await response.json();

      if (result.success) {
        // Kiểm tra xem có cấu hình labels không
        const hasConfig = result.data?.config?.labels &&
                         Object.keys(result.data.config.labels).length > 0;
      } else {
        console.error('Labels check error:', result.error);
      }
    } catch (error) {
      console.error('Error checking labels config:', error);
    }
  };

  // Xử lý mở trang quản lý Mooly
  const handleOpenMoolyDashboard = async (accountData) => {
    try {
      // Gọi API để lấy SSO link - chỉ cần truyền userId, token sẽ được lấy từ biến môi trường
      const response = await fetch(`/api/mooly-proxy/get-sso-link?userId=${accountData.userId}`);
      const result = await response.json();

      if (result.success && result.data?.url) {
        // Mở SSO link trong tab mới
        window.open(result.data.url, '_blank');
      } else {
        console.error('SSO link error:', result.error);
        toast.error('Không thể lấy đường dẫn truy cập. Vui lòng thử lại sau.');
      }
    } catch (error) {
      console.error('Error opening Mooly dashboard:', error);
      toast.error('Đã xảy ra lỗi khi truy cập trang quản lý Mooly.');
    }
  };

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading || isLoadingAccount || isLoadingChatbots) {
    return <LoadingScreen />;
  }

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
        <Typography variant="h4">Quản lý Channel</Typography>

        {account ? (
          <Button
            variant="contained"
            color="primary"
            startIcon={<Iconify icon="eva:external-link-fill" />}
            onClick={() => handleOpenMoolyDashboard(account)}
          >
            Truy cập trang quản lý
          </Button>
        ) : (
          <Button
            variant="contained"
            color="primary"
            startIcon={<Iconify icon="eva:plus-fill" />}
            onClick={handleOpenRegisterDialog}
          >
            Khởi tạo tài khoản
          </Button>
        )}
      </Stack>

      {!account ? (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <AlertTitle>Chưa có tài khoản Mooly</AlertTitle>
          <Typography variant="body2">
            Bạn cần khởi tạo tài khoản Mooly để sử dụng tính năng quản lý channel chatbot.
          </Typography>
        </Alert>
      ) : (
        <>
          {/* Hiển thị cảnh báo nếu chưa có cấu hình labels */}
          {/* {!hasLabelsConfig && (
            <Alert
              severity="warning"
              sx={{ mb: 3 }}
              action={
                <Button
                  color="warning"
                  variant="outlined"
                  size="small"
                  onClick={handleInitializeLabels}
                  disabled={isInitializingLabels}
                  startIcon={isInitializingLabels ? <CircularProgress size={16} /> : <Iconify icon="mdi:label-outline" />}
                >
                  {isInitializingLabels ? 'Đang khởi tạo...' : 'Khởi tạo Labels'}
                </Button>
              }
            >
              <AlertTitle>Chưa có cấu hình Labels</AlertTitle>
              <Typography variant="body2">
                Tài khoản Mooly của bạn chưa được cấu hình các labels cần thiết cho chatbot AI.
                Hãy khởi tạo labels để sử dụng đầy đủ tính năng phân loại tin nhắn.
              </Typography>
            </Alert>
          )} */}

          {isLoadingMoolyChannels || isLoadingConnectedChannels ? (
            <LoadingScreen />
          ) : (
            <>
              <Card sx={{ mb: 3 }}>
                <Tabs
                  value={tabValue}
                  onChange={handleChangeTab}
                  sx={{
                    px: 2,
                    bgcolor: 'background.neutral',
                  }}
                >
                  <Tab label="Tất cả" />
                  <Tab label="Đã kết nối" />
                  <Tab label="Chưa kết nối" />
                </Tabs>

                <Box sx={{ p: 2 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1 }}>
                    {tabValue === 0 && 'Tất cả channel'}
                    {tabValue === 1 && 'Channel đã kết nối với chatbot'}
                    {tabValue === 2 && 'Channel chưa kết nối với chatbot'}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {tabValue === 0 && 'Danh sách tất cả channel từ tài khoản Mooly của bạn'}
                    {tabValue === 1 &&
                      'Danh sách channel đã được kết nối với chatbot AI trên hệ thống'}
                    {tabValue === 2 &&
                      'Danh sách channel chưa được kết nối với chatbot AI trên hệ thống'}
                  </Typography>
                </Box>
              </Card>

              {isNotFound ? (
                <Card>
                  <Box sx={{ textAlign: 'center', py: 10 }}>
                    {/* Hiển thị loading nếu đang tải dữ liệu */}
                    {isLoadingMoolyChannels ? (
                      <>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          Đang tải dữ liệu...
                        </Typography>
                        <LoadingScreen sx={{ height: 'auto', py: 5 }} />
                      </>
                    ) : (
                      <>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          {tabValue === 0 && 'Không tìm thấy channel nào'}
                          {tabValue === 1 && 'Không tìm thấy channel nào đã kết nối'}
                          {tabValue === 2 && 'Không tìm thấy channel nào chưa kết nối'}
                        </Typography>
                        <Typography variant="body2">
                          {tabValue === 0 &&
                            'Không có channel nào trong tài khoản Mooly của bạn. Hãy tạo channel trên hệ thống app.mooly.vn.'}
                          {tabValue === 1 &&
                            'Chưa có channel nào được kết nối với chatbot. Hãy kết nối channel với chatbot.'}
                          {tabValue === 2 && 'Tất cả channel đã được kết nối với chatbot.'}
                        </Typography>
                      </>
                    )}
                  </Box>
                </Card>
              ) : (
                <>
                  {/* Hiển thị loading khi đang tải dữ liệu */}
                  {isLoadingMoolyChannels || isLoadingConnectedChannels ? (
                    <Box sx={{ textAlign: 'center', py: 5 }}>
                      <Typography variant="h6" sx={{ mb: 2 }}>
                        Đang tải danh sách channel...
                      </Typography>
                      <LoadingScreen sx={{ height: 'auto', py: 5 }} />
                    </Box>
                  ) : (
                    <Grid container spacing={3}>
                      {filteredChannels.map((channel) => (
                        <Grid key={channel.id} item size={{ xs: 12, sm: 6, md: 4 }}>
                          <ChannelCard
                            channel={channel}
                            onConnect={handleOpenConnectDialog}
                            onToggleActive={handleToggleActive}
                            onAutomation={handleOpenAutomationDialog}
                          />
                        </Grid>
                      ))}
                    </Grid>
                  )}
                </>
              )}
            </>
          )}
        </>
      )}

      {/* Dialog cập nhật tài khoản Mooly đã bị loại bỏ */}

      {/* Dialog đăng ký tài khoản Mooly */}
      {openRegisterDialog && (
        <MoolyRegisterDialog
          open={openRegisterDialog}
          onClose={handleCloseRegisterDialog}
        />
      )}

      {/* Dialog kết nối channel với chatbot */}
      {openConnectDialog && (
        <ChannelConnectDialog
          open={openConnectDialog}
          onClose={handleCloseConnectDialog}
          channel={selectedChannel}
          connection={selectedChannel?.connection}
          chatbots={chatbots}
          isCreating={isCreatingChannel}
          isUpdating={isUpdatingChannel}
          onConnect={handleConnectChannel}
        />
      )}

      {/* Dialog xóa kết nối channel */}
      {openDeleteDialog && (
        <ChannelDeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          channel={selectedChannel}
          isDeleting={isDeletingChannel}
          onDelete={handleDeleteChannel}
        />
      )}

      {/* Dialog cấu hình automation */}
      {openAutomationDialog && (
        <ChannelAutomationDialog
          open={openAutomationDialog}
          onClose={handleCloseAutomationDialog}
          channel={selectedChannel}
        />
      )}
    </Container>
  );
}
